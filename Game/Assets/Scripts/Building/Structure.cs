using System;
using System.Collections.Generic;
using UnityEngine;

namespace Building
{
    public class Structure : MonoBehaviour
    {
        public int GridSize = 1;
        public List<StructureVoxel> Voxels;

        private void Awake()
        {
            foreach (var voxel in Voxels)
            {
                voxel.SetParent(this);
            }
        }

        public Bounds GetBounds()
        {
            if (Voxels == null || Voxels.Count == 0)
            {
                return new Bounds(transform.position, Vector3.zero);
            }

            // Start with the first voxel's bounds
            Bounds combinedBounds = GetVoxelBounds(Voxels[0]);

            // Encapsulate all other voxel bounds
            for (int i = 1; i < Voxels.Count; i++)
            {
                combinedBounds.Encapsulate(GetVoxelBounds(Voxels[i]));
            }

            return combinedBounds;
        }

        private Bounds GetVoxelBounds(StructureVoxel voxel)
        {
            if (voxel == null)
            {
                return new Bounds(Vector3.zero, Vector3.zero);
            }

            // Try to get bounds from Renderer first
            var renderer = voxel.GetComponent<Renderer>();
            if (renderer != null)
            {
                return renderer.bounds;
            }

            // Try to get bounds from Collider
            var collider = voxel.GetComponent<Collider>();
            if (collider != null)
            {
                return collider.bounds;
            }

            // Fallback to transform position with GridSize
            return new Bounds(voxel.transform.position, Vector3.one * GridSize);
        }
    }
}
