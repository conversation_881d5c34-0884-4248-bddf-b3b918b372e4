using System;
using System.Collections.Generic;
using UnityEngine;

namespace Building
{
    public class Structure : MonoBehaviour
    {
        public int GridSize = 1;
        public List<StructureVoxel> Voxels;

        private void Awake()
        {
            foreach (var voxel in Voxels)
            {
                voxel.SetParent(this);
            }
        }

        public Bounds GetBounds()
        {
            if (Voxels == null || Voxels.Count == 0)
            {
                return new Bounds(transform.position, Vector3.zero);
            }

            var combinedBounds = GetVoxelBounds(Voxels[0]);
            for (var i = 1; i < Voxels.Count; i++)
            {
                combinedBounds.Encapsulate(GetVoxelBounds(Voxels[i]));
            }

            return combinedBounds;
        }

        private Bounds GetVoxelBounds(StructureVoxel voxel)
        {
            if (voxel == null || voxel.BoxCollider == null)
            {
                return new Bounds(Vector3.zero, Vector3.zero);
            }

            return voxel.BoxCollider.bounds;
        }
    }
}
